<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek AI Code Editor</title>
    <meta name="description" content="Professional AI-powered code editor with DeepSeek integration">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github-dark.min.css">
    <style>
        :root {
            /* Dark theme colors */
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3e3e42;
            --text-primary: #d4d4d4;
            --text-secondary: #858585;
            --text-tertiary: #cccccc;
            --primary-accent: #007acc;
            --primary-hover: #1177bb;
            --secondary-accent: #6c757d;
            --secondary-hover: #5a6268;
            --success-color: #4ec9b0;
            --error-color: #f44747;
            --warning-color: #d7ba7d;
            --line-highlight: rgba(0, 122, 204, 0.2);
            --line-highlight-border: rgba(0, 122, 204, 0.4);
            --selection-bg: #264f78;
        }

        [data-theme="light"] {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f5f5f5;
            --bg-tertiary: #eaeaea;
            --border-color: #d1d1d1;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-tertiary: #444444;
            --primary-accent: #0066b8;
            --primary-hover: #005aa3;
            --secondary-accent: #5a6268;
            --secondary-hover: #495057;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --line-highlight: rgba(0, 102, 184, 0.1);
            --line-highlight-border: rgba(0, 102, 184, 0.2);
            --selection-bg: #b3d7ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            flex-direction: column;
            line-height: 1.6;
        }

        .header {
            background: var(--bg-tertiary);
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .header h1 {
            color: var(--primary-accent);
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .api-config {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .api-config label {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .api-config input {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            font-size: 0.85rem;
            min-width: 250px;
            transition: border-color 0.2s;
        }

        .api-config input:focus {
            outline: none;
            border-color: var(--primary-accent);
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
            position: relative;
        }

        .editor-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
            min-width: 0; /* Fix for flexbox overflow */
        }

        .toolbar {
            background: var(--bg-tertiary);
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        .btn {
            background: var(--primary-accent);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .btn:hover {
            background: var(--primary-hover);
        }

        .btn:disabled {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            cursor: not-allowed;
        }

        .btn-secondary {
            background: var(--secondary-accent);
        }

        .btn-secondary:hover {
            background: var(--secondary-hover);
        }

        .btn-icon {
            padding: 0.5rem;
            border-radius: 4px;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-right: 0.5rem;
            border-right: 1px solid var(--border-color);
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .line-info {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-left: auto;
        }

        .editor-wrapper {
            flex: 1;
            position: relative;
            overflow: hidden;
            display: flex;
        }

        .line-numbers {
            width: 50px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 0.5rem 0.25rem;
            text-align: right;
            overflow: hidden;
            user-select: none;
        }

        .code-editor-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .code-editor {
            width: 100%;
            height: 100%;
            background: var(--bg-primary);
            color: var(--text-primary);
            border: none;
            outline: none;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 0.5rem;
            resize: none;
            white-space: pre;
            overflow-wrap: normal;
            overflow-x: auto;
            tab-size: 4;
        }

        .code-editor:focus {
            box-shadow: inset 0 0 0 1px var(--primary-accent);
        }

        .selected-lines {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .highlight-line {
            position: absolute;
            left: 0;
            right: 0;
            background: var(--line-highlight);
            border: 1px solid var(--line-highlight-border);
        }

        .sidebar {
            width: 380px;
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            background: var(--bg-tertiary);
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h3 {
            font-size: 1rem;
            font-weight: 600;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            font-size: 1rem;
        }

        .sidebar-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .prompt-section {
            margin-bottom: 0.5rem;
        }

        .prompt-section label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-tertiary);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .prompt-input {
            width: 100%;
            min-height: 100px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem;
            border-radius: 4px;
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
            transition: border-color 0.2s;
        }

        .prompt-input:focus {
            outline: none;
            border-color: var(--primary-accent);
        }

        .selected-lines-display {
            margin-bottom: 0.5rem;
        }

        .selected-lines-display label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-tertiary);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .selected-lines-list {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 0.75rem;
            min-height: 60px;
            color: var(--text-primary);
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 150px;
        }

        .ai-response-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            overflow-y: auto;
        }

        .message {
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .loading {
            color: var(--primary-accent);
            font-style: italic;
        }

        .error {
            color: var(--error-color);
            background: rgba(244, 71, 71, 0.1);
            border-left: 3px solid var(--error-color);
        }

        .success {
            color: var(--success-color);
            background: rgba(78, 201, 176, 0.1);
            border-left: 3px solid var(--success-color);
        }

        .warning {
            color: var(--warning-color);
            background: rgba(215, 186, 125, 0.1);
            border-left: 3px solid var(--warning-color);
        }

        .diff-viewer {
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 0.85rem;
            white-space: pre;
            overflow-x: auto;
            padding: 0.5rem;
            background: var(--bg-tertiary);
            border-radius: 4px;
            margin-top: 0.5rem;
        }

        .diff-added {
            color: var(--success-color);
            background: rgba(78, 201, 176, 0.1);
        }

        .diff-removed {
            color: var(--error-color);
            background: rgba(244, 71, 71, 0.1);
        }

        .status-bar {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 0.25rem 1rem;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-ready {
            background: var(--success-color);
        }

        .status-processing {
            background: var(--primary-accent);
        }

        .status-error {
            background: var(--error-color);
        }

        .language-selector {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.35rem 0.75rem;
            border-radius: 4px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: border-color 0.2s;
        }

        .language-selector:focus {
            outline: none;
            border-color: var(--primary-accent);
        }

        .toggle-sidebar-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-right: none;
            border-radius: 4px 0 0 4px;
            padding: 0.5rem;
            cursor: pointer;
            z-index: 10;
            display: none;
        }

        @media (max-width: 1200px) {
            .sidebar {
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 400px;
                max-width: 90vw;
                z-index: 5;
                box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
            }
            
            .toggle-sidebar-btn {
                display: block;
            }
            
            .api-config input {
                min-width: 180px;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-wrap: wrap;
                gap: 0.75rem;
                padding: 0.75rem;
            }
            
            .header h1 {
                font-size: 1.1rem;
            }
            
            .header-actions {
                width: 100%;
                order: 3;
                margin-top: 0.5rem;
                justify-content: space-between;
            }
            
            .toolbar {
                gap: 0.5rem;
                padding: 0.5rem;
            }
            
            .btn {
                padding: 0.4rem 0.75rem;
                font-size: 0.8rem;
            }
            
            .language-selector {
                padding: 0.3rem 0.5rem;
                font-size: 0.8rem;
            }
            
            .line-info {
                font-size: 0.75rem;
            }
            
            .sidebar {
                width: 90vw;
            }
        }

        /* Highlight.js overrides */
        .hljs {
            background: var(--bg-primary);
            padding: 0;
        }
        
        /* Selection styling */
        ::selection {
            background: var(--selection-bg);
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="#007ACC"/>
                <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16Z" fill="#007ACC"/>
                <path d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="#007ACC"/>
            </svg>
            DeepSeek AI Code Editor
        </h1>
        
        <div class="header-actions">
            <button class="theme-toggle" id="theme-toggle">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 3C10.9 3 10 3.9 10 5C10 6.1 10.9 7 12 7C13.1 7 14 6.1 14 5C14 3.9 13.1 3 12 3ZM12 17C10.9 17 10 17.9 10 19C10 20.1 10.9 21 12 21C13.1 21 14 20.1 14 19C14 17.9 13.1 17 12 17ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="currentColor"/>
                </svg>
                Theme
            </button>
            
            <div class="api-config">
                <label for="api-key">API Key:</label>
                <input type="password" id="api-key" placeholder="Enter your DeepSeek API key">
            </div>
        </div>
    </div>

    <div class="main-container">
        <div class="editor-container">
            <div class="toolbar">
                <div class="toolbar-group">
                    <button class="btn" id="analyze-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="white"/>
                            <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16Z" fill="white"/>
                            <path d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="white"/>
                        </svg>
                        Analyze
                    </button>
                    <button class="btn btn-secondary" id="optimize-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="white"/>
                            <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16C9.79 16 8 14.21 8 12C8 9.79 9.79 8 12 8C14.21 8 16 9.79 16 12C16 14.21 14.21 16 12 16Z" fill="white"/>
                            <path d="M12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="white"/>
                        </svg>
                        Optimize
                    </button>
                </div>
                
                <div class="toolbar-group">
                    <button class="btn btn-secondary" id="select-all-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 5H21V7H3V5ZM3 9H21V11H3V9ZM3 13H21V15H3V13ZM3 17H21V19H3V17Z" fill="white"/>
                        </svg>
                        Select All
                    </button>
                    <button class="btn btn-secondary" id="clear-selection-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="white"/>
                        </svg>
                        Clear
                    </button>
                </div>
                
                <select class="language-selector" id="language-select">
                    <option value="javascript">JavaScript</option>
                    <option value="python">Python</option>
                    <option value="java">Java</option>
                    <option value="csharp">C#</option>
                    <option value="cpp">C++</option>
                    <option value="go">Go</option>
                    <option value="rust">Rust</option>
                    <option value="php">PHP</option>
                    <option value="ruby">Ruby</option>
                    <option value="swift">Swift</option>
                    <option value="kotlin">Kotlin</option>
                    <option value="typescript">TypeScript</option>
                    <option value="html">HTML</option>
                    <option value="css">CSS</option>
                    <option value="sql">SQL</option>
                </select>
                
                <div class="line-info">
                    Lines: <span id="line-count">1</span> | 
                    Selected: <span id="selected-count">0</span>
                </div>
            </div>

            <div class="editor-wrapper">
                <div class="line-numbers" id="line-numbers">1</div>
                <div class="code-editor-container">
                    <div class="selected-lines" id="selected-lines"></div>
                    <pre id="highlighted-code"><code class="language-javascript" id="code-content"></code></pre>
                    <textarea 
                        class="code-editor" 
                        id="code-editor" 
                        placeholder="Start typing your code here..."
                        spellcheck="false"
                    ></textarea>
                </div>
            </div>
        </div>

        <button class="toggle-sidebar-btn" id="toggle-sidebar-btn">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.41 16.59L10.83 12L15.41 7.41L14 6L8 12L14 18L15.41 16.59Z" fill="currentColor"/>
            </svg>
        </button>

        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>AI Assistant</h3>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                    </svg>
                </button>
            </div>
            <div class="sidebar-content">
                <div class="prompt-section">
                    <label for="user-prompt">What would you like to change?</label>
                    <textarea 
                        id="user-prompt" 
                        class="prompt-input" 
                        placeholder="Describe what you want to change in the selected code (e.g., 'Refactor this function to be more efficient', 'Add error handling', 'Explain this code')"
                    ></textarea>
                </div>

                <div class="selected-lines-display">
                    <label>Selected Lines Preview:</label>
                    <div class="selected-lines-list" id="selected-lines-list">
                        No lines selected
                    </div>
                </div>

                <button class="btn" id="apply-changes-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z" fill="white"/>
                    </svg>
                    Process with AI
                </button>

                <div class="ai-response-container" id="ai-response">
                    <div class="message">
                        <p>Welcome to DeepSeek AI Code Editor! Select code and enter instructions to get started.</p>
                        <p>Try:</p>
                        <ul>
                            <li>"Explain this code"</li>
                            <li>"Refactor for better performance"</li>
                            <li>"Add error handling"</li>
                            <li>"Convert to TypeScript"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <div>
            <span class="status-indicator status-ready"></span>
            <span id="status-type">Ready</span>
        </div>
        <span id="status-text"></span>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/csharp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/cpp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/rust.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/php.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/ruby.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/swift.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/kotlin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/typescript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/html.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/sql.min.js"></script>
    <script>
        /**
         * DeepSeek AI Code Editor - Professional Edition
         * A sophisticated code editor with AI-powered assistance
         */
        class AICodeEditor {
            constructor() {
                // DOM Elements
                this.editor = document.getElementById('code-editor');
                this.highlightedCode = document.getElementById('code-content');
                this.lineNumbers = document.getElementById('line-numbers');
                this.selectedLinesList = document.getElementById('selected-lines-list');
                this.aiResponseContainer = document.getElementById('ai-response');
                this.languageSelector = document.getElementById('language-select');
                this.selectedLines = new Set();
                this.lineCount = 1;
                this.isSidebarCollapsed = false;
                this.currentTheme = 'dark';
                
                // Initialize components
                this.initializeEditor();
                this.bindEvents();
                this.updateLineNumbers();
                this.applySyntaxHighlighting();
                this.setupThemeToggle();
                this.setupSidebarToggle();
                
                // Set initial sample code
                this.setSampleCode();
            }

            initializeEditor() {
                // Set up syntax highlighting
                hljs.highlightAll();
                
                // Configure editor defaults
                this.editor.style.fontFamily = "'Fira Code', 'Courier New', monospace";
                this.editor.style.lineHeight = '1.5';
                this.editor.style.tabSize = '4';
            }

            bindEvents() {
                // Editor events
                this.editor.addEventListener('input', () => {
                    this.updateLineNumbers();
                    this.applySyntaxHighlighting();
                });
                
                this.editor.addEventListener('scroll', () => this.syncScroll());
                this.editor.addEventListener('click', (e) => this.handleLineClick(e));
                this.editor.addEventListener('keydown', (e) => this.handleKeyEvents(e));
                
                // Toolbar buttons
                document.getElementById('select-all-btn').addEventListener('click', () => this.selectAllLines());
                document.getElementById('clear-selection-btn').addEventListener('click', () => this.clearSelection());
                document.getElementById('analyze-btn').addEventListener('click', () => this.analyzeCode());
                document.getElementById('optimize-btn').addEventListener('click', () => this.optimizeCode());
                document.getElementById('apply-changes-btn').addEventListener('click', () => this.applyAIChanges());
                
                // Language selector
                this.languageSelector.addEventListener('change', () => {
                    this.applySyntaxHighlighting();
                });
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('theme-toggle');
                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    
                    // Update theme toggle icon
                    themeToggle.innerHTML = this.currentTheme === 'dark' 
                        ? `<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 3C10.9 3 10 3.9 10 5C10 6.1 10.9 7 12 7C13.1 7 14 6.1 14 5C14 3.9 13.1 3 12 3ZM12 17C10.9 17 10 17.9 10 19C10 20.1 10.9 21 12 21C13.1 21 14 20.1 14 19C14 17.9 13.1 17 12 17ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" fill="currentColor"/>
                        </svg> Theme`
                        : `<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41.39.39 1.03.39 1.41 0l1.06-1.06z" fill="currentColor"/>
                        </svg> Theme`;
                    
                    // Re-apply syntax highlighting with new theme
                    this.applySyntaxHighlighting();
                });
            }

            setupSidebarToggle() {
                const sidebarToggle = document.getElementById('sidebar-toggle');
                const toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
                const sidebar = document.getElementById('sidebar');
                
                sidebarToggle.addEventListener('click', () => {
                    this.isSidebarCollapsed = !this.isSidebarCollapsed;
                    sidebar.classList.toggle('collapsed', this.isSidebarCollapsed);
                    toggleSidebarBtn.style.display = this.isSidebarCollapsed ? 'block' : 'none';
                });
                
                toggleSidebarBtn.addEventListener('click', () => {
                    this.isSidebarCollapsed = false;
                    sidebar.classList.remove('collapsed');
                    toggleSidebarBtn.style.display = 'none';
                });
                
                // Check initial screen size
                this.checkSidebarVisibility();
                window.addEventListener('resize', () => this.checkSidebarVisibility());
            }

            checkSidebarVisibility() {
                const sidebar = document.getElementById('sidebar');
                const toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
                
                if (window.innerWidth <= 1200) {
                    sidebar.classList.add('collapsed');
                    toggleSidebarBtn.style.display = 'block';
                    this.isSidebarCollapsed = true;
                } else {
                    sidebar.classList.remove('collapsed');
                    toggleSidebarBtn.style.display = 'none';
                    this.isSidebarCollapsed = false;
                }
            }

            setSampleCode() {
                const sampleCode = `// AI-Powered Code Editor
function calculateSum(numbers) {
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    return sum;
}

function findMaxValue(array) {
    if (array.length === 0) return null;
    let max = array[0];
    for (let i = 1; i < array.length; i++) {
        if (array[i] > max) {
            max = array[i];
        }
    }
    return max;
}

// Example usage
const numbers = [1, 2, 3, 4, 5];
console.log("Sum:", calculateSum(numbers));
console.log("Max:", findMaxValue(numbers));`;
                
                this.editor.value = sampleCode;
                this.updateLineNumbers();
                this.applySyntaxHighlighting();
            }

            updateLineNumbers() {
                const lines = this.editor.value.split('\n');
                this.lineCount = lines.length;
                
                // Generate line numbers
                let numbersHTML = '';
                for (let i = 1; i <= this.lineCount; i++) {
                    numbersHTML += `${i}\n`;
                }
                
                this.lineNumbers.innerHTML = numbersHTML;
                document.getElementById('line-count').textContent = this.lineCount;
                
                this.updateSelectedLinesDisplay();
                this.updateHighlights();
            }

            syncScroll() {
                this.lineNumbers.scrollTop = this.editor.scrollTop;
                document.getElementById('highlighted-code').scrollTop = this.editor.scrollTop;
                document.getElementById('selected-lines').scrollTop = this.editor.scrollTop;
            }

            applySyntaxHighlighting() {
                const code = this.editor.value;
                const language = this.languageSelector.value;
                
                this.highlightedCode.textContent = code;
                this.highlightedCode.className = `language-${language}`;
                hljs.highlightElement(this.highlightedCode);
            }

            handleLineClick(e) {
                const textArea = this.editor;
                const cursorPosition = textArea.selectionStart;
                const textBeforeCursor = textArea.value.substring(0, cursorPosition);
                const lineNumber = textBeforeCursor.split('\n').length;
                
                // Handle multi-select with Ctrl/Cmd key
                if (e.ctrlKey || e.metaKey) {
                    if (this.selectedLines.has(lineNumber)) {
                        this.selectedLines.delete(lineNumber);
                    } else {
                        this.selectedLines.add(lineNumber);
                    }
                } 
                // Handle range selection with Shift key
                else if (e.shiftKey && this.selectedLines.size > 0) {
                    const lastSelected = Math.max(...Array.from(this.selectedLines));
                    const start = Math.min(lineNumber, lastSelected);
                    const end = Math.max(lineNumber, lastSelected);
                    
                    this.selectedLines.clear();
                    for (let i = start; i <= end; i++) {
                        this.selectedLines.add(i);
                    }
                }
                // Single line selection
                else {
                    this.selectedLines.clear();
                    this.selectedLines.add(lineNumber);
                }
                
                this.updateSelectedLinesDisplay();
                this.updateHighlights();
            }

            handleKeyEvents(e) {
                // Handle tab key
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.editor.selectionStart;
                    const end = this.editor.selectionEnd;
                    
                    // Insert tab character
                    this.editor.value = this.editor.value.substring(0, start) + '\t' + this.editor.value.substring(end);
                    
                    // Move cursor position
                    this.editor.selectionStart = this.editor.selectionEnd = start + 1;
                    
                    // Update highlighting
                    this.applySyntaxHighlighting();
                }
                
                // Handle line selection with keyboard
                if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                    if (e.shiftKey) {
                        e.preventDefault();
                        const cursorPosition = this.editor.selectionStart;
                        const textBeforeCursor = this.editor.value.substring(0, cursorPosition);
                        const lineNumber = textBeforeCursor.split('\n').length;
                        
                        if (e.key === 'ArrowUp' && lineNumber > 1) {
                            this.selectedLines.add(lineNumber - 1);
                        } else if (e.key === 'ArrowDown' && lineNumber < this.lineCount) {
                            this.selectedLines.add(lineNumber + 1);
                        }
                        
                        this.updateSelectedLinesDisplay();
                        this.updateHighlights();
                    }
                }
            }

            selectAllLines() {
                this.selectedLines.clear();
                for (let i = 1; i <= this.lineCount; i++) {
                    this.selectedLines.add(i);
                }
                this.updateSelectedLinesDisplay();
                this.updateHighlights();
            }

            clearSelection() {
                this.selectedLines.clear();
                this.updateSelectedLinesDisplay();
                this.updateHighlights();
            }

            updateSelectedLinesDisplay() {
                const selectedCount = this.selectedLines.size;
                document.getElementById('selected-count').textContent = selectedCount;
                
                if (selectedCount === 0) {
                    this.selectedLinesList.textContent = 'No lines selected';
                    return;
                }
                
                const lines = this.editor.value.split('\n');
                const selectedLinesContent = Array.from(this.selectedLines)
                    .sort((a, b) => a - b)
                    .map(lineNum => {
                        const lineContent = lines[lineNum - 1] || '';
                        return `${lineNum}: ${lineContent}`;
                    })
                    .join('\n');
                
                this.selectedLinesList.textContent = selectedLinesContent;
                
                // Apply syntax highlighting to the selected lines preview
                const language = this.languageSelector.value;
                this.selectedLinesList.innerHTML = hljs.highlight(selectedLinesContent, { language }).value;
            }

            updateHighlights() {
                const highlightContainer = document.getElementById('selected-lines');
                highlightContainer.innerHTML = '';
                
                if (this.selectedLines.size === 0) return;
                
                const lineHeight = parseInt(getComputedStyle(this.editor).lineHeight);
                const paddingTop = parseFloat(getComputedStyle(this.editor).paddingTop);
                const editorRect = this.editor.getBoundingClientRect();
                const lineNumbersRect = this.lineNumbers.getBoundingClientRect();
                
                this.selectedLines.forEach(lineNum => {
                    const highlight = document.createElement('div');
                    highlight.className = 'highlight-line';
                    
                    // Calculate position based on line number
                    highlight.style.top = `${paddingTop + (lineNum - 1) * lineHeight}px`;
                    highlight.style.height = `${lineHeight}px`;
                    highlight.style.left = `${lineNumbersRect.width}px`;
                    highlight.style.width = `${editorRect.width - lineNumbersRect.width}px`;
                    
                    highlightContainer.appendChild(highlight);
                });
            }

            analyzeCode() {
                const code = this.editor.value;
                const language = this.languageSelector.value;
                
                if (!code.trim()) {
                    this.showStatus('No code to analyze', 'error');
                    return;
                }
                
                // Show loading state
                this.showStatus('Analyzing code...', 'processing');
                
                // Simulate analysis (in a real app, this would call an API)
                setTimeout(() => {
                    const lines = code.split('\n');
                    const analysis = {
                        totalLines: lines.length,
                        nonEmptyLines: lines.filter(line => line.trim()).length,
                        comments: lines.filter(line => {
                            const trimmed = line.trim();
                            return trimmed.startsWith('//') || 
                                   trimmed.startsWith('/*') || 
                                   trimmed.startsWith('#') || 
                                   trimmed.startsWith('--');
                        }).length,
                        functions: (code.match(/(function|def|fn|func)\s+\w+/g) || []).length,
                        variables: (code.match(/(?:let|const|var|def|val|var)\s+\w+/g) || []).length,
                        complexity: this.calculateComplexity(code)
                    };
                    
                    const analysisResult = `
                        <div class="message success">
                            <h4>Code Analysis Results</h4>
                            <ul>
                                <li><strong>Total lines:</strong> ${analysis.totalLines}</li>
                                <li><strong>Non-empty lines:</strong> ${analysis.nonEmptyLines}</li>
                                <li><strong>Comments:</strong> ${analysis.comments} (${Math.round(analysis.comments / analysis.totalLines * 100)}%)</li>
                                <li><strong>Functions:</strong> ${analysis.functions}</li>
                                <li><strong>Variables:</strong> ${analysis.variables}</li>
                                <li><strong>Complexity:</strong> ${analysis.complexity}</li>
                            </ul>
                            <p>Considerations:</p>
                            <ul>
                                <li>${analysis.comments / analysis.totalLines < 0.1 ? '⚠️ Add more comments to improve code readability' : '✅ Good comment density'}</li>
                                <li>${analysis.complexity > 10 ? '⚠️ High complexity detected - consider refactoring' : '✅ Complexity at reasonable level'}</li>
                                <li>${analysis.functions > 15 ? '⚠️ Large number of functions - consider modularizing' : '✅ Function count looks good'}</li>
                            </ul>
                        </div>
                    `;
                    
                    this.aiResponseContainer.innerHTML = analysisResult;
                    this.showStatus('Analysis complete', 'ready');
                }, 1000);
            }

            calculateComplexity(code) {
                // Simple complexity calculation (in a real app, use a proper complexity metric)
                const lines = code.split('\n');
                let complexity = 0;
                
                lines.forEach(line => {
                    const trimmed = line.trim();
                    
                    // Increase complexity for control flow statements
                    if (trimmed.match(/(if|else|for|while|switch|case|catch)\s*\(?/)) {
                        complexity += 2;
                    } else if (trimmed.match(/function|def|fn|func/)) {
                        complexity += 1;
                    } else if (trimmed.match(/\?\s*:/)) { // Ternary operator
                        complexity += 1;
                    }
                });
                
                return complexity;
            }

            optimizeCode() {
                if (this.selectedLines.size === 0) {
                    this.showStatus('Please select lines to optimize', 'error');
                    return;
                }
                
                const selectedLinesArray = Array.from(this.selectedLines).sort((a, b) => a - b);
                const lines = this.editor.value.split('\n');
                const selectedCode = selectedLinesArray.map(lineNum => lines[lineNum - 1]).join('\n');
                
                this.showStatus('Optimizing selected code...', 'processing');
                
                // In a real app, this would call the AI API
                setTimeout(() => {
                    // Simulated optimization
                    const optimizedCode = `// Optimized version
function calculateSum(numbers) {
    return numbers.reduce((acc, curr) => acc + curr, 0);
}

function findMaxValue(array) {
    return array.length ? Math.max(...array) : null;
}`;
                    
                    const diff = this.createDiffView(selectedCode, optimizedCode);
                    
                    this.aiResponseContainer.innerHTML = `
                        <div class="message success">
                            <h4>Optimization Suggestions</h4>
                            <p>Here's an optimized version of your code:</p>
                            ${diff}
                            <button class="btn" onclick="editor.replaceSelectedLines(\`${optimizedCode.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">
                                Apply Optimized Code
                            </button>
                        </div>
                    `;
                    
                    this.showStatus('Optimization complete', 'ready');
                }, 1500);
            }

            createDiffView(oldCode, newCode) {
                const oldLines = oldCode.split('\n');
                const newLines = newCode.split('\n');
                let diffHTML = '<div class="diff-viewer">';
                
                // Simple diff visualization
                for (let i = 0; i < Math.max(oldLines.length, newLines.length); i++) {
                    const oldLine = oldLines[i] || '';
                    const newLine = newLines[i] || '';
                    
                    if (oldLine !== newLine) {
                        if (oldLine && newLine) {
                            diffHTML += `<div class="diff-removed">- ${oldLine}</div>`;
                            diffHTML += `<div class="diff-added">+ ${newLine}</div>`;
                        } else if (oldLine) {
                            diffHTML += `<div class="diff-removed">- ${oldLine}</div>`;
                        } else {
                            diffHTML += `<div class="diff-added">+ ${newLine}</div>`;
                        }
                    } else {
                        diffHTML += `<div>  ${newLine}</div>`;
                    }
                }
                
                diffHTML += '</div>';
                return diffHTML;
            }

            async applyAIChanges() {
                const apiKey = document.getElementById('api-key').value;
                const userPrompt = document.getElementById('user-prompt').value;
                const originalCode = this.editor.value;
                
                if (!apiKey) {
                    this.showStatus('Please enter your DeepSeek API key', 'error');
                    return;
                }
                
                if (!userPrompt.trim()) {
                    this.showStatus('Please enter an instruction', 'error');
                    return;
                }
                
                if (this.selectedLines.size === 0) {
                    this.showStatus('Please select lines to modify', 'error');
                    return;
                }
                
                const selectedLinesArray = Array.from(this.selectedLines).sort((a, b) => a - b);
                const lines = originalCode.split('\n');
                const selectedCode = selectedLinesArray.map(lineNum => lines[lineNum - 1]).join('\n');
                
                try {
                    this.showStatus('Processing AI request...', 'processing');
                    document.getElementById('apply-changes-btn').disabled = true;
                    
                    // In a real app, this would call the actual API
                    const result = await this.simulateAIRequest(originalCode, selectedLinesArray, userPrompt);
                    
                    // Apply the AI changes
                    this.applyCodeChanges(result, selectedLinesArray);
                    this.showStatus('AI changes applied successfully', 'ready');
                    
                } catch (error) {
                    console.error('AI request failed:', error);
                    this.showStatus(`Error: ${error.message}`, 'error');
                } finally {
                    document.getElementById('apply-changes-btn').disabled = false;
                }
            }

            async simulateAIRequest(originalCode, selectedLines, userPrompt) {
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Simulate different responses based on the prompt
                if (userPrompt.toLowerCase().includes('explain')) {
                    return `// Explanation of the selected code:
// This code calculates the sum of all numbers in an array by iterating through each element
// and accumulating the total in a variable called 'sum'. The time complexity is O(n) where
// n is the number of elements in the array, as it needs to visit each element once.

// For optimization, consider using the reduce() method:
// return numbers.reduce((acc, curr) => acc + curr, 0);`;
                } else if (userPrompt.toLowerCase().includes('refactor')) {
                    return `// Refactored version:
function calculateSum(numbers) {
    return numbers.reduce((acc, curr) => acc + curr, 0);
}

function findMaxValue(array) {
    return array.length ? Math.max(...array) : null;
}`;
                } else if (userPrompt.toLowerCase().includes('error')) {
                    return `// Version with error handling:
function calculateSum(numbers) {
    if (!Array.isArray(numbers)) {
        throw new TypeError('Input must be an array');
    }
    
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        if (typeof numbers[i] !== 'number') {
            throw new TypeError('All array elements must be numbers');
        }
        sum += numbers[i];
    }
    return sum;
}`;
                } else {
                    return `// Here's the modified code based on your request:
function calculateSum(numbers) {
    let total = 0;
    numbers.forEach(num => {
        total += num;
    });
    return total;
}`;
                }
            }

            applyCodeChanges(aiResponse, selectedLines) {
                const responseElement = document.getElementById('ai-response');
                
                // Format the AI response with syntax highlighting
                const language = this.languageSelector.value;
                const highlightedResponse = hljs.highlight(aiResponse, { language }).value;
                
                responseElement.innerHTML = `
                    <div class="message success">
                        <h4>AI Response</h4>
                        <div class="code-block">${highlightedResponse}</div>
                        <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                            <button class="btn" onclick="editor.replaceSelectedLines(\`${aiResponse.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">
                                Apply Changes
                            </button>
                            <button class="btn btn-secondary" onclick="editor.copyToClipboard(\`${aiResponse.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">
                                Copy to Clipboard
                            </button>
                        </div>
                    </div>
                `;
            }

            replaceSelectedLines(newCode) {
                if (this.selectedLines.size === 0) {
                    this.showStatus('No lines selected to replace', 'error');
                    return;
                }
                
                const lines = this.editor.value.split('\n');
                const selectedLinesArray = Array.from(this.selectedLines).sort((a, b) => a - b);
                const firstLine = selectedLinesArray[0] - 1;
                const lastLine = selectedLinesArray[selectedLinesArray.length - 1];
                
                // Replace the selected lines with the new code
                const newLines = newCode.split('\n');
                const updatedCode = [
                    ...lines.slice(0, firstLine),
                    ...newLines,
                    ...lines.slice(lastLine)
                ].join('\n');
                
                this.editor.value = updatedCode;
                this.updateLineNumbers();
                this.applySyntaxHighlighting();
                this.clearSelection();
                
                this.showStatus('Code replaced successfully', 'ready');
            }

            copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showStatus('Copied to clipboard', 'ready');
                }).catch(err => {
                    console.error('Failed to copy:', err);
                    this.showStatus('Failed to copy to clipboard', 'error');
                });
            }

            showStatus(message, type = 'info') {
                const statusText = document.getElementById('status-text');
                const statusType = document.getElementById('status-type');
                const statusIndicator = document.querySelector('.status-indicator');
                
                // Clear all classes
                statusIndicator.className = 'status-indicator';
                
                // Set new status
                statusText.textContent = message;
                
                switch (type) {
                    case 'ready':
                        statusType.textContent = 'Ready';
                        statusIndicator.classList.add('status-ready');
                        break;
                    case 'processing':
                        statusType.textContent = 'Processing';
                        statusIndicator.classList.add('status-processing');
                        break;
                    case 'error':
                        statusType.textContent = 'Error';
                        statusIndicator.classList.add('status-error');
                        break;
                    default:
                        statusType.textContent = 'Info';
                }
                
                // Clear status after 5 seconds if it's not an error
                if (type !== 'error') {
                    setTimeout(() => {
                        if (statusText.textContent === message) {
                            statusText.textContent = '';
                            statusType.textContent = 'Ready';
                            statusIndicator.className = 'status-indicator status-ready';
                        }
                    }, 5000);
                }
            }
        }

        // Initialize the editor when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            const editor = new AICodeEditor();
            
            // Make editor globally accessible for demo purposes
            window.editor = editor;
            
            // Set dark theme by default
            document.documentElement.setAttribute('data-theme', 'dark');
        });
    </script>
</body>
</html>